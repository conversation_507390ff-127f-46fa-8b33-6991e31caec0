const fs = require('fs');
const path = require('path');
const { app } = require('electron');

/**
 * SpotifyInteractionRecorder - Records user interactions during Spotify login flow
 * for later automated replay
 */
class SpotifyInteractionRecorder {
  constructor() {
    this.isRecording = false;
    this.recordedInteractions = [];
    this.recordingStartTime = null;
    this.configPath = path.join(app.getPath('userData'), 'spotify-login-recording.json');
  }

  /**
   * Start recording user interactions
   */
  startRecording() {
    this.isRecording = true;
    this.recordedInteractions = [];
    this.recordingStartTime = Date.now();
    console.log('[Spotify Recorder] Started recording interactions');
  }

  /**
   * Stop recording and save interactions
   */
  async stopRecording() {
    if (!this.isRecording) {
      return;
    }

    this.isRecording = false;
    console.log(`[Spotify Recorder] Stopped recording. Captured ${this.recordedInteractions.length} interactions`);
    
    await this.saveRecording();
  }

  /**
   * Record a user interaction
   * @param {Object} interaction - Interaction details
   */
  recordInteraction(interaction) {
    if (!this.isRecording) {
      return;
    }

    const timestamp = Date.now() - this.recordingStartTime;
    const recordedInteraction = {
      ...interaction,
      timestamp,
      id: this.generateInteractionId()
    };

    this.recordedInteractions.push(recordedInteraction);
    console.log(`[Spotify Recorder] Recorded interaction:`, recordedInteraction.type, recordedInteraction.selector);
  }

  /**
   * Generate unique interaction ID
   * @returns {string} Unique ID
   */
  generateInteractionId() {
    return `interaction_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Save recorded interactions to file
   */
  async saveRecording() {
    try {
      const recordingData = {
        version: '1.0',
        recordedAt: new Date().toISOString(),
        totalInteractions: this.recordedInteractions.length,
        totalDuration: Date.now() - this.recordingStartTime,
        interactions: this.recordedInteractions
      };

      await fs.promises.writeFile(
        this.configPath,
        JSON.stringify(recordingData, null, 2),
        'utf8'
      );

      console.log(`[Spotify Recorder] Saved ${this.recordedInteractions.length} interactions to ${this.configPath}`);
    } catch (error) {
      console.error('[Spotify Recorder] Failed to save recording:', error);
    }
  }

  /**
   * Load saved recording
   * @returns {Object|null} Recording data or null if not found
   */
  async loadRecording() {
    try {
      if (!fs.existsSync(this.configPath)) {
        return null;
      }

      const recordingData = await fs.promises.readFile(this.configPath, 'utf8');
      const recording = JSON.parse(recordingData);
      
      console.log(`[Spotify Recorder] Loaded recording with ${recording.totalInteractions} interactions`);
      return recording;
    } catch (error) {
      console.error('[Spotify Recorder] Failed to load recording:', error);
      return null;
    }
  }

  /**
   * Check if a recording exists
   * @returns {boolean} True if recording exists
   */
  hasRecording() {
    return fs.existsSync(this.configPath);
  }

  /**
   * Delete saved recording
   */
  async deleteRecording() {
    try {
      if (fs.existsSync(this.configPath)) {
        await fs.promises.unlink(this.configPath);
        console.log('[Spotify Recorder] Deleted saved recording');
      }
    } catch (error) {
      console.error('[Spotify Recorder] Failed to delete recording:', error);
    }
  }

  /**
   * Inject recording script into web page
   * @param {Object} webContents - Electron webContents object
   */
  async injectRecordingScript(webContents) {
    if (!this.isRecording) {
      return;
    }

    try {
      await webContents.executeJavaScript(`
        (function() {
          // Prevent multiple injections
          if (window.spotifyRecorderInjected) {
            return;
          }
          window.spotifyRecorderInjected = true;

          console.log('[Spotify Recorder] Injecting recording script');

          // Function to generate CSS selector for an element
          function generateSelector(element) {
            if (!element) return null;
            
            // Try ID first
            if (element.id) {
              return '#' + element.id;
            }
            
            // Try data attributes
            const testId = element.getAttribute('data-testid');
            if (testId) {
              return '[data-testid="' + testId + '"]';
            }
            
            const dataId = element.getAttribute('data-id');
            if (dataId) {
              return '[data-id="' + dataId + '"]';
            }
            
            // Try name attribute for form elements
            if (element.name) {
              return '[name="' + element.name + '"]';
            }
            
            // Try type for input elements
            if (element.type && element.tagName.toLowerCase() === 'input') {
              return 'input[type="' + element.type + '"]';
            }
            
            // Try class names (use first meaningful class)
            if (element.className && typeof element.className === 'string') {
              const classes = element.className.split(' ').filter(c => 
                c && !c.match(/^(css-|sc-|emotion-|styled-)/));
              if (classes.length > 0) {
                return '.' + classes[0];
              }
            }
            
            // Fall back to tag name with position
            const tagName = element.tagName.toLowerCase();
            const siblings = Array.from(element.parentNode?.children || [])
              .filter(el => el.tagName.toLowerCase() === tagName);
            
            if (siblings.length > 1) {
              const index = siblings.indexOf(element);
              return tagName + ':nth-of-type(' + (index + 1) + ')';
            }
            
            return tagName;
          }

          // Function to get element text content
          function getElementText(element) {
            if (!element) return '';
            return (element.textContent || element.innerText || '').trim().substring(0, 100);
          }

          // Function to send interaction to main process
          function recordInteraction(interaction) {
            // Send via IPC if available
            if (window.electronAPI && window.electronAPI.recordSpotifyInteraction) {
              window.electronAPI.recordSpotifyInteraction(interaction);
            } else if (window.require) {
              // Direct IPC access
              try {
                const { ipcRenderer } = window.require('electron');
                ipcRenderer.send('record-spotify-interaction', interaction);
              } catch (e) {
                // Fallback: store in window object for polling
                if (!window.spotifyRecordedInteractions) {
                  window.spotifyRecordedInteractions = [];
                }
                window.spotifyRecordedInteractions.push(interaction);
              }
            } else {
              // Fallback: store in window object for polling
              if (!window.spotifyRecordedInteractions) {
                window.spotifyRecordedInteractions = [];
              }
              window.spotifyRecordedInteractions.push(interaction);
            }
          }

          // Record click events
          document.addEventListener('click', function(event) {
            const selector = generateSelector(event.target);
            const interaction = {
              type: 'click',
              selector: selector,
              tagName: event.target.tagName.toLowerCase(),
              text: getElementText(event.target),
              url: window.location.href,
              coordinates: {
                x: event.clientX,
                y: event.clientY
              }
            };
            
            recordInteraction(interaction);
            console.log('[Recorder] Click:', interaction);
          }, true);

          // Record input events
          document.addEventListener('input', function(event) {
            if (event.target.tagName.toLowerCase() === 'input' || 
                event.target.tagName.toLowerCase() === 'textarea') {
              
              const selector = generateSelector(event.target);
              const interaction = {
                type: 'input',
                selector: selector,
                tagName: event.target.tagName.toLowerCase(),
                inputType: event.target.type || 'text',
                value: event.target.value,
                placeholder: event.target.placeholder || '',
                url: window.location.href
              };
              
              recordInteraction(interaction);
              console.log('[Recorder] Input:', interaction);
            }
          }, true);

          // Record form submissions
          document.addEventListener('submit', function(event) {
            const selector = generateSelector(event.target);
            const formData = new FormData(event.target);
            const formFields = {};
            
            for (let [key, value] of formData.entries()) {
              formFields[key] = value;
            }
            
            const interaction = {
              type: 'submit',
              selector: selector,
              tagName: event.target.tagName.toLowerCase(),
              formFields: formFields,
              url: window.location.href
            };
            
            recordInteraction(interaction);
            console.log('[Recorder] Submit:', interaction);
          }, true);

          // Record page navigation
          let lastUrl = window.location.href;
          const checkUrlChange = () => {
            if (window.location.href !== lastUrl) {
              const interaction = {
                type: 'navigation',
                fromUrl: lastUrl,
                toUrl: window.location.href,
                title: document.title
              };
              
              recordInteraction(interaction);
              console.log('[Recorder] Navigation:', interaction);
              lastUrl = window.location.href;
            }
          };
          
          // Check for URL changes periodically
          setInterval(checkUrlChange, 1000);
          
          // Also listen for popstate events
          window.addEventListener('popstate', checkUrlChange);

          console.log('[Spotify Recorder] Recording script injected successfully');
        })();
      `);
    } catch (error) {
      console.error('[Spotify Recorder] Failed to inject recording script:', error);
    }
  }

  /**
   * Poll for recorded interactions from web page
   * @param {Object} webContents - Electron webContents object
   */
  async pollRecordedInteractions(webContents) {
    if (!this.isRecording) {
      return;
    }

    try {
      const interactions = await webContents.executeJavaScript(`
        (function() {
          if (window.spotifyRecordedInteractions) {
            const interactions = window.spotifyRecordedInteractions;
            window.spotifyRecordedInteractions = [];
            return interactions;
          }
          return [];
        })();
      `);

      interactions.forEach(interaction => {
        this.recordInteraction(interaction);
      });
    } catch (error) {
      console.error('[Spotify Recorder] Failed to poll interactions:', error);
    }
  }
}

module.exports = SpotifyInteractionRecorder;
